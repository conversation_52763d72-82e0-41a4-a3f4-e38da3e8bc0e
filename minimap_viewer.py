#!/usr/bin/env python3
"""
Minimap Viewer Component for FiendishFinder

A PyQt6-based minimap viewer with zoom functionality, floor navigation,
and camera position preservation for Tibia minimap exploration.
"""

import sys
import os
from pathlib import Path
from typing import Dict, Optional, Tuple
import logging

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QGraphicsView, QGraphicsScene, QGraphicsPixmapItem, QPushButton,
    QComboBox, QLabel, QSlider, QFrame, QSizePolicy, QMessageBox,
    QGraphicsLineItem, QGraphicsRectItem
)
from PyQt6.QtCore import Qt, QRectF, pyqtSignal, QPointF
from PyQt6.QtGui import QPixmap, QWheelEvent, QMouseEvent, QPainter, QKeyEvent, QPen, QColor

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class MinimapGraphicsView(QGraphicsView):
    """
    Custom QGraphicsView for minimap display with enhanced zoom and pan capabilities.
    """
    
    # Signal emitted when the view is transformed (zoom/pan)
    viewTransformed = pyqtSignal(float, QPointF)  # zoom_factor, center_point
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # Configure view settings
        self.setDragMode(QGraphicsView.DragMode.NoDrag)
        self.setRenderHint(QPainter.RenderHint.Antialiasing)
        self.setRenderHint(QPainter.RenderHint.SmoothPixmapTransform)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        # Zoom settings
        self.zoom_factor = 1.0
        self.min_zoom = 0.1
        self.max_zoom = 20.0  # Increased for pixel-precise selection
        self.zoom_step = 1.15
        
        # Pan settings
        self.pan_enabled = True
        self.last_pan_point = QPointF()
        self.is_panning = False
        
        # Camera position for preservation across floor changes - per floor storage
        self.floor_camera_positions = {}  # Dict[int, dict] - stores center and zoom per floor
        self.current_floor_id = None

        # Crosshair functionality - GLOBAL crosshair for all floors
        self.crosshair_horizontal: Optional[QGraphicsLineItem] = None
        self.crosshair_vertical: Optional[QGraphicsLineItem] = None
        self.crosshair_center_square: Optional[QGraphicsRectItem] = None  # Square outline at center
        self.global_crosshair_position: Optional[QPointF] = None  # Global position for all floors
        
    def wheelEvent(self, event: QWheelEvent):
        """Handle mouse wheel events for zooming."""
        # Enable zoom with mouse wheel (no modifier required)
        zoom_in = event.angleDelta().y() > 0
        self.zoom(zoom_in, event.position())
    
    def mousePressEvent(self, event: QMouseEvent):
        """Handle mouse press events for panning and crosshair placement."""
        if event.button() == Qt.MouseButton.LeftButton:
            self.is_panning = True
            self.last_pan_point = event.position()
            self.setCursor(Qt.CursorShape.ClosedHandCursor)
        elif event.button() == Qt.MouseButton.RightButton:
            # Right-click to place crosshairs with pixel snapping
            # FIX: Use QPointF to preserve floating-point precision and avoid pixel offset
            mouse_pos = event.position()

            # Convert floating-point mouse coordinates to scene coordinates
            # Use QPointF to preserve sub-pixel precision, then convert to QPoint for mapToScene
            mouse_point = QPointF(mouse_pos.x(), mouse_pos.y())
            scene_pos = self.mapToScene(mouse_point.toPoint())

            # Snap to pixel center for exact game coordinate alignment
            snapped_pos = QPointF(
                round(scene_pos.x()) + 0.5,  # Center of pixel
                round(scene_pos.y()) + 0.5   # Center of pixel
            )

            # Debug logging to verify coordinate accuracy
            logger.info(f"Mouse click at viewport ({mouse_pos.x():.3f}, {mouse_pos.y():.3f}) -> "
                       f"scene ({scene_pos.x():.3f}, {scene_pos.y():.3f}) -> "
                       f"snapped ({snapped_pos.x():.1f}, {snapped_pos.y():.1f})")

            self.place_crosshairs(snapped_pos)
        else:
            super().mousePressEvent(event)
    
    def mouseMoveEvent(self, event: QMouseEvent):
        """Handle mouse move events for panning."""
        if self.is_panning:
            delta = event.position() - self.last_pan_point
            self.last_pan_point = event.position()
            
            # Pan the view
            self.horizontalScrollBar().setValue(
                self.horizontalScrollBar().value() - int(delta.x())
            )
            self.verticalScrollBar().setValue(
                self.verticalScrollBar().value() - int(delta.y())
            )
        else:
            super().mouseMoveEvent(event)
    
    def mouseReleaseEvent(self, event: QMouseEvent):
        """Handle mouse release events."""
        if event.button() == Qt.MouseButton.LeftButton:
            self.is_panning = False
            self.setCursor(Qt.CursorShape.ArrowCursor)
            self.emit_view_transformed()
        else:
            super().mouseReleaseEvent(event)
    
    def zoom(self, zoom_in: bool, center_point: Optional[QPointF] = None):
        """
        Zoom the view in or out.
        
        Args:
            zoom_in: True to zoom in, False to zoom out
            center_point: Point to zoom towards (in widget coordinates)
        """
        if zoom_in:
            factor = self.zoom_step
        else:
            factor = 1.0 / self.zoom_step
        
        new_zoom = self.zoom_factor * factor
        
        # Clamp zoom level
        if new_zoom < self.min_zoom or new_zoom > self.max_zoom:
            return
        
        self.zoom_factor = new_zoom
        
        # Set transform anchor to mouse position if provided
        if center_point:
            self.setTransformationAnchor(QGraphicsView.ViewportAnchor.NoAnchor)
            # FIX: Use QPointF to preserve floating-point precision for zoom anchoring
            center_pointf = QPointF(center_point.x(), center_point.y())
            old_pos = self.mapToScene(center_pointf.toPoint())
            self.scale(factor, factor)
            new_pos = self.mapToScene(center_pointf.toPoint())
            delta = new_pos - old_pos
            self.translate(delta.x(), delta.y())
        else:
            self.setTransformationAnchor(QGraphicsView.ViewportAnchor.AnchorViewCenter)
            self.scale(factor, factor)
        
        self.emit_view_transformed()
    
    def zoom_to_factor(self, target_zoom: float):
        """Set zoom to a specific factor using absolute transformation."""
        if target_zoom < self.min_zoom or target_zoom > self.max_zoom:
            return

        # Use absolute transformation to avoid accumulation errors
        # Reset transform and apply target zoom directly
        self.resetTransform()
        self.scale(target_zoom, target_zoom)
        self.zoom_factor = target_zoom
        self.emit_view_transformed()
    
    def fit_in_view_with_margin(self, margin_percent: float = 0.1):
        """Fit the scene in view with a margin."""
        if self.scene():
            scene_rect = self.scene().itemsBoundingRect()
            if not scene_rect.isEmpty():
                # Add margin
                margin_x = scene_rect.width() * margin_percent
                margin_y = scene_rect.height() * margin_percent
                scene_rect.adjust(-margin_x, -margin_y, margin_x, margin_y)
                
                self.fitInView(scene_rect, Qt.AspectRatioMode.KeepAspectRatio)
                
                # Update zoom factor
                transform = self.transform()
                self.zoom_factor = transform.m11()
                self.emit_view_transformed()
    
    def save_camera_position(self, floor_id=None):
        """Save current camera position and zoom for later restoration."""
        if floor_id is None:
            floor_id = self.current_floor_id

        if floor_id is None:
            logger.warning("Cannot save camera position - no floor ID specified")
            return

        # Get the current viewport center and map it to scene coordinates
        viewport_center = self.viewport().rect().center()
        scene_center = self.mapToScene(viewport_center)

        # Only save if we have a valid scene position
        if not scene_center.isNull() and self.scene():
            # Store position for this specific floor
            self.floor_camera_positions[floor_id] = {
                'center': QPointF(scene_center),
                'zoom': self.zoom_factor
            }

            # Detailed logging for debugging
            scene_rect = self.scene().sceneRect()
            transform = self.transform()
            logger.info(f"SAVE Floor {floor_id} - Center: ({scene_center.x():.2f}, {scene_center.y():.2f}), "
                       f"Zoom: {self.zoom_factor:.4f}, Scene: {scene_rect.width():.0f}x{scene_rect.height():.0f}, "
                       f"Transform: [{transform.m11():.4f}, {transform.m12():.4f}, {transform.m13():.4f}]")
        else:
            logger.warning("Could not save camera position - invalid scene coordinates")
    
    def restore_camera_position(self, floor_id=None):
        """Restore previously saved camera position and zoom."""
        if floor_id is None:
            floor_id = self.current_floor_id

        if floor_id is None or floor_id not in self.floor_camera_positions:
            logger.info(f"No saved camera position for floor {floor_id}")
            return

        # Get saved position for this floor
        saved_data = self.floor_camera_positions[floor_id]
        saved_center = QPointF(saved_data['center'])
        saved_zoom = saved_data['zoom']

        # Log state before restoration
        scene_rect_before = self.scene().sceneRect() if self.scene() else QRectF()
        center_before = self.mapToScene(self.viewport().rect().center())

        # Restore zoom using absolute transformation
        self.zoom_to_factor(saved_zoom)

        # Use more precise positioning method instead of centerOn()
        # Calculate the required scroll bar positions to center the target point
        viewport_rect = self.viewport().rect()
        target_viewport_center = self.mapFromScene(saved_center)

        # Calculate the offset from current viewport center to target
        current_viewport_center = viewport_rect.center()
        offset_x = target_viewport_center.x() - current_viewport_center.x()
        offset_y = target_viewport_center.y() - current_viewport_center.y()

        # Adjust scroll bars to achieve precise positioning
        h_scroll = self.horizontalScrollBar()
        v_scroll = self.verticalScrollBar()
        h_scroll.setValue(h_scroll.value() + int(offset_x))
        v_scroll.setValue(v_scroll.value() + int(offset_y))

        # Log state after restoration
        center_after = self.mapToScene(self.viewport().rect().center())
        transform = self.transform()
        logger.info(f"RESTORE Floor {floor_id} - Target: ({saved_center.x():.2f}, {saved_center.y():.2f}), "
                   f"Before: ({center_before.x():.2f}, {center_before.y():.2f}), "
                   f"After: ({center_after.x():.2f}, {center_after.y():.2f}), "
                   f"Scene: {scene_rect_before.width():.0f}x{scene_rect_before.height():.0f}, "
                   f"Transform: [{transform.m11():.4f}, {transform.m12():.4f}, {transform.m13():.4f}]")

        self.emit_view_transformed()
    
    def emit_view_transformed(self):
        """Emit signal when view is transformed."""
        center = self.mapToScene(self.viewport().rect().center())
        self.viewTransformed.emit(self.zoom_factor, center)

    def place_crosshairs(self, scene_pos: QPointF):
        """
        Place global crosshairs at the specified scene position.

        Args:
            scene_pos: Position in scene coordinates where to place crosshairs (pixel-snapped)
        """
        if not self.scene():
            return

        # Remove existing crosshairs
        self.remove_crosshairs()

        # Store the GLOBAL crosshair position (same for all floors)
        self.global_crosshair_position = QPointF(scene_pos)

        # Get scene bounds
        scene_rect = self.scene().sceneRect()

        # Create super thin crosshair pen - BLACK lines
        line_pen = QPen(QColor(0, 0, 0, 200))  # Black, slightly transparent
        line_pen.setWidthF(0.25)  # Super thin lines (0.25 pixels)
        line_pen.setStyle(Qt.PenStyle.SolidLine)

        # Create horizontal line (spans full width of scene)
        self.crosshair_horizontal = QGraphicsLineItem(
            scene_rect.left(), scene_pos.y(),
            scene_rect.right(), scene_pos.y()
        )
        self.crosshair_horizontal.setPen(line_pen)
        self.crosshair_horizontal.setZValue(1000)  # Ensure it's drawn on top
        self.scene().addItem(self.crosshair_horizontal)

        # Create vertical line (spans full height of scene)
        self.crosshair_vertical = QGraphicsLineItem(
            scene_pos.x(), scene_rect.top(),
            scene_pos.x(), scene_rect.bottom()
        )
        self.crosshair_vertical.setPen(line_pen)
        self.crosshair_vertical.setZValue(1000)  # Ensure it's drawn on top
        self.scene().addItem(self.crosshair_vertical)

        # Create center square outline - very thin BLACK outline
        square_pen = QPen(QColor(0, 0, 0, 255))  # Black, fully opaque
        square_pen.setWidthF(0.1)  # Very thin outline (0.1 pixels)
        square_pen.setStyle(Qt.PenStyle.SolidLine)

        # Create a 1x1 pixel square centered on the clicked position
        square_size = 1.0
        self.crosshair_center_square = QGraphicsRectItem(
            scene_pos.x() - square_size/2, scene_pos.y() - square_size/2,
            square_size, square_size
        )
        self.crosshair_center_square.setPen(square_pen)
        from PyQt6.QtGui import QBrush
        self.crosshair_center_square.setBrush(QBrush(Qt.BrushStyle.NoBrush))  # No fill, just outline
        self.crosshair_center_square.setZValue(1001)  # Above the lines
        self.scene().addItem(self.crosshair_center_square)

        logger.info(f"Crosshairs placed at ({scene_pos.x():.2f}, {scene_pos.y():.2f}) on floor {self.current_floor_id}")

    def remove_crosshairs(self):
        """Remove existing crosshairs from the scene."""
        if self.crosshair_horizontal and self.scene():
            try:
                self.scene().removeItem(self.crosshair_horizontal)
            except RuntimeError:
                # Item was already deleted (e.g., when scene was cleared)
                pass
            self.crosshair_horizontal = None

        if self.crosshair_vertical and self.scene():
            try:
                self.scene().removeItem(self.crosshair_vertical)
            except RuntimeError:
                # Item was already deleted (e.g., when scene was cleared)
                pass
            self.crosshair_vertical = None

        if self.crosshair_center_square and self.scene():
            try:
                self.scene().removeItem(self.crosshair_center_square)
            except RuntimeError:
                # Item was already deleted (e.g., when scene was cleared)
                pass
            self.crosshair_center_square = None

        self.global_crosshair_position = None

    def restore_crosshairs(self, floor_id: int = None):
        """
        Restore global crosshairs if they exist.

        Args:
            floor_id: Ignored - crosshairs are global across all floors
        """
        if not self.scene():
            logger.warning("Cannot restore crosshairs - no scene available")
            return

        if self.global_crosshair_position is not None:
            logger.info(f"Restoring global crosshairs at ({self.global_crosshair_position.x():.2f}, {self.global_crosshair_position.y():.2f})")
            self.place_crosshairs(self.global_crosshair_position)
        else:
            logger.debug("No global crosshairs to restore")

    def save_crosshair_position(self, floor_id: int = None):
        """
        Save current global crosshair position (no-op since crosshairs are already global).

        Args:
            floor_id: Ignored - crosshairs are global
        """
        # No need to save - crosshairs are already global
        if self.global_crosshair_position is not None:
            logger.debug("Global crosshair position is already saved")

    def clear_all_crosshairs(self):
        """Clear global crosshairs."""
        self.remove_crosshairs()
        logger.info("Cleared global crosshairs")


class MinimapViewer(QWidget):
    """
    Main minimap viewer widget with floor navigation and zoom controls.
    """
    
    # Signal emitted when floor changes
    floorChanged = pyqtSignal(int)  # floor_number
    
    def __init__(self, minimap_dir: str = "processed_minimap", parent=None):
        super().__init__(parent)
        
        self.minimap_dir = Path(minimap_dir)
        self.current_floor = 7  # Default floor (floor_07)
        self.floor_images: Dict[int, QPixmap] = {}
        
        # Floor ordering as specified
        self.floor_order = [
            # Floors above main (descending order)
            0, 1, 2, 3, 4, 5, 6,
            # Main floor
            7,
            # Floors below main (ascending order)  
            8, 9, 10, 11, 12, 13, 14, 15
        ]
        
        self.setup_ui()
        self.load_floor_images()
        self.set_floor(self.current_floor)
    
    def setup_ui(self):
        """Set up the user interface."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # Create controls panel
        controls_frame = QFrame()
        controls_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        controls_layout = QHBoxLayout(controls_frame)
        
        # Floor navigation
        floor_label = QLabel("Floor:")
        self.floor_down_btn = QPushButton("-")
        self.floor_down_btn.setMaximumWidth(30)
        self.floor_down_btn.setToolTip("Go down one floor")
        self.floor_down_btn.clicked.connect(self.floor_down)

        self.floor_combo = QComboBox()
        self.floor_combo.setMinimumWidth(100)
        self.floor_combo.currentTextChanged.connect(self.on_floor_changed)

        self.floor_up_btn = QPushButton("+")
        self.floor_up_btn.setMaximumWidth(30)
        self.floor_up_btn.setToolTip("Go up one floor")
        self.floor_up_btn.clicked.connect(self.floor_up)
        
        # Zoom controls
        zoom_label = QLabel("Zoom:")
        self.zoom_slider = QSlider(Qt.Orientation.Horizontal)
        self.zoom_slider.setMinimum(10)  # 0.1x zoom
        self.zoom_slider.setMaximum(2000)  # 20.0x zoom
        self.zoom_slider.setValue(100)  # 1.0x zoom
        self.zoom_slider.setMinimumWidth(150)
        self.zoom_slider.valueChanged.connect(self.on_zoom_slider_changed)
        
        self.zoom_in_btn = QPushButton("Zoom In")
        self.zoom_out_btn = QPushButton("Zoom Out")
        self.fit_view_btn = QPushButton("Fit View")
        
        self.zoom_in_btn.clicked.connect(self.zoom_in)
        self.zoom_out_btn.clicked.connect(self.zoom_out)
        self.fit_view_btn.clicked.connect(self.fit_view)
        
        # Add controls to layout
        controls_layout.addWidget(floor_label)
        controls_layout.addWidget(self.floor_down_btn)
        controls_layout.addWidget(self.floor_combo)
        controls_layout.addWidget(self.floor_up_btn)
        controls_layout.addStretch()
        controls_layout.addWidget(zoom_label)
        controls_layout.addWidget(self.zoom_slider)
        controls_layout.addWidget(self.zoom_in_btn)
        controls_layout.addWidget(self.zoom_out_btn)
        controls_layout.addWidget(self.fit_view_btn)
        
        # Create graphics view
        self.graphics_view = MinimapGraphicsView()
        self.graphics_scene = QGraphicsScene()
        self.graphics_view.setScene(self.graphics_scene)
        
        # Connect view signals
        self.graphics_view.viewTransformed.connect(self.on_view_transformed)
        
        # Current minimap item
        self.current_minimap_item: Optional[QGraphicsPixmapItem] = None
        
        # Add widgets to main layout
        layout.addWidget(controls_frame)
        layout.addWidget(self.graphics_view, 1)  # Give graphics view most space
        
        # Set size policy
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        # Enable keyboard focus for shortcuts
        self.setFocusPolicy(Qt.FocusPolicy.StrongFocus)

    def load_floor_images(self):
        """Load all available floor images."""
        logger.info(f"Loading floor images from {self.minimap_dir}")

        if not self.minimap_dir.exists():
            logger.error(f"Minimap directory not found: {self.minimap_dir}")
            return

        loaded_floors = []
        for floor in self.floor_order:
            floor_file = self.minimap_dir / f"floor_{floor:02d}.png"
            if floor_file.exists():
                try:
                    pixmap = QPixmap(str(floor_file))
                    if not pixmap.isNull():
                        self.floor_images[floor] = pixmap
                        loaded_floors.append(floor)
                        logger.debug(f"Loaded floor {floor}: {pixmap.width()}x{pixmap.height()}")
                    else:
                        logger.warning(f"Failed to load floor image: {floor_file}")
                except Exception as e:
                    logger.error(f"Error loading floor {floor}: {e}")
            else:
                logger.debug(f"Floor file not found: {floor_file}")

        # Populate floor combo box
        self.floor_combo.blockSignals(True)  # Temporarily block signals
        self.floor_combo.clear()
        for floor in self.floor_order:
            if floor in self.floor_images:
                if floor == 7:
                    self.floor_combo.addItem(f"Floor {floor:02d} (Main)", floor)
                else:
                    self.floor_combo.addItem(f"Floor {floor:02d}", floor)
        self.floor_combo.blockSignals(False)  # Re-enable signals

        logger.info(f"Loaded {len(loaded_floors)} floor images: {loaded_floors}")

    def set_floor(self, floor: int):
        """
        Set the current floor and display its minimap.

        Args:
            floor: Floor number to display
        """
        if floor not in self.floor_images:
            logger.warning(f"Floor {floor} not available")
            return

        # Save current camera position before changing floors
        if self.current_minimap_item is not None and self.graphics_view.current_floor_id is not None:
            self.graphics_view.save_camera_position(self.graphics_view.current_floor_id)

        self.current_floor = floor
        self.graphics_view.current_floor_id = floor

        # Clear scene (this will remove crosshairs too)
        self.graphics_scene.clear()

        # Reset crosshair references since scene.clear() deleted them
        self.graphics_view.crosshair_horizontal = None
        self.graphics_view.crosshair_vertical = None
        self.graphics_view.crosshair_center_square = None
        # Note: Don't reset global_crosshair_position - we want to restore it

        # Add new floor image
        pixmap = self.floor_images[floor]
        self.current_minimap_item = QGraphicsPixmapItem(pixmap)
        self.graphics_scene.addItem(self.current_minimap_item)

        # Set scene rect to image bounds
        new_scene_rect = self.current_minimap_item.boundingRect()
        self.graphics_scene.setSceneRect(new_scene_rect)

        # Log scene change
        logger.info(f"FLOOR_CHANGE - Floor {floor}: Scene rect {new_scene_rect.width():.0f}x{new_scene_rect.height():.0f}")

        # Restore camera position if this isn't the first floor load
        if hasattr(self, '_first_floor_loaded'):
            self.graphics_view.restore_camera_position(floor)
        else:
            # First time loading - fit view
            self.fit_view()
            self._first_floor_loaded = True

        # Restore crosshairs for this floor (if any exist)
        self.graphics_view.restore_crosshairs(floor)

        # Update combo box selection
        for i in range(self.floor_combo.count()):
            if self.floor_combo.itemData(i) == floor:
                self.floor_combo.setCurrentIndex(i)
                break

        logger.info(f"Switched to floor {floor}")
        self.floorChanged.emit(floor)

    def on_floor_changed(self, floor_text: str):
        """Handle floor selection change from combo box."""
        current_data = self.floor_combo.currentData()
        if current_data is not None:
            self.set_floor(current_data)

    def floor_up(self):
        """Navigate to the next floor up (lower floor number)."""
        current_index = self.floor_order.index(self.current_floor)
        if current_index > 0:  # Not at the top floor
            next_floor = self.floor_order[current_index - 1]
            if next_floor in self.floor_images:
                self.set_floor(next_floor)

    def floor_down(self):
        """Navigate to the next floor down (higher floor number)."""
        current_index = self.floor_order.index(self.current_floor)
        if current_index < len(self.floor_order) - 1:  # Not at the bottom floor
            next_floor = self.floor_order[current_index + 1]
            if next_floor in self.floor_images:
                self.set_floor(next_floor)

    def keyPressEvent(self, event: QKeyEvent):
        """Handle keyboard shortcuts."""
        if event.key() == Qt.Key.Key_Plus or event.key() == Qt.Key.Key_Equal:
            self.floor_up()
        elif event.key() == Qt.Key.Key_Minus:
            self.floor_down()
        elif event.key() == Qt.Key.Key_C and event.modifiers() == Qt.KeyboardModifier.ControlModifier:
            # Ctrl+C to clear crosshairs
            self.clear_crosshairs()
        else:
            super().keyPressEvent(event)

    def clear_crosshairs(self):
        """Clear global crosshairs."""
        self.graphics_view.clear_all_crosshairs()
        logger.info("Cleared global crosshairs")

    def zoom_in(self):
        """Zoom in the minimap."""
        self.graphics_view.zoom(True)

    def zoom_out(self):
        """Zoom out the minimap."""
        self.graphics_view.zoom(False)

    def fit_view(self):
        """Fit the minimap in the view."""
        self.graphics_view.fit_in_view_with_margin(0.05)

    def on_zoom_slider_changed(self, value: int):
        """Handle zoom slider value change."""
        zoom_factor = value / 100.0  # Convert to 0.1 - 20.0 range
        self.graphics_view.zoom_to_factor(zoom_factor)

    def on_view_transformed(self, zoom_factor: float, center_point: QPointF):
        """Handle view transformation updates."""
        # Update zoom slider without triggering signal
        self.zoom_slider.blockSignals(True)
        self.zoom_slider.setValue(int(zoom_factor * 100))
        self.zoom_slider.blockSignals(False)

    def get_current_floor(self) -> int:
        """Get the currently displayed floor."""
        return self.current_floor

    def get_available_floors(self) -> list[int]:
        """Get list of available floors."""
        return list(self.floor_images.keys())

    def get_camera_info(self) -> dict:
        """Get current camera information."""
        center = self.graphics_view.mapToScene(self.graphics_view.viewport().rect().center())
        return {
            'floor': self.current_floor,
            'zoom_factor': self.graphics_view.zoom_factor,
            'center_x': center.x(),
            'center_y': center.y()
        }


class MinimapViewerWindow(QMainWindow):
    """
    Main window for the minimap viewer application.
    """

    def __init__(self, minimap_dir: str = "processed_minimap"):
        super().__init__()

        self.setWindowTitle("FiendishFinder - Minimap Viewer")
        self.setGeometry(100, 100, 1200, 800)

        # Create central widget
        self.minimap_viewer = MinimapViewer(minimap_dir)
        self.setCentralWidget(self.minimap_viewer)

        # Connect signals
        self.minimap_viewer.floorChanged.connect(self.on_floor_changed)

        # Status bar
        self.status_bar = self.statusBar()
        self.update_status()

    def on_floor_changed(self, floor: int):
        """Handle floor change events."""
        self.update_status()

    def update_status(self):
        """Update status bar with current information."""
        camera_info = self.minimap_viewer.get_camera_info()
        available_floors = len(self.minimap_viewer.get_available_floors())

        status_text = (
            f"Floor: {camera_info['floor']:02d} | "
            f"Zoom: {camera_info['zoom_factor']:.1f}x | "
            f"Position: ({camera_info['center_x']:.0f}, {camera_info['center_y']:.0f}) | "
            f"Available Floors: {available_floors}"
        )

        self.status_bar.showMessage(status_text)


def main():
    """Main function to run the minimap viewer application."""
    app = QApplication(sys.argv)

    # Set application properties
    app.setApplicationName("FiendishFinder Minimap Viewer")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("FiendishFinder")

    # Create and show main window
    window = MinimapViewerWindow()

    # Check if minimap directory exists
    minimap_dir = Path("processed_minimap")
    if not minimap_dir.exists():
        QMessageBox.warning(
            window,
            "Directory Not Found",
            f"Minimap directory '{minimap_dir}' not found.\n"
            "Please ensure the processed minimap images are available."
        )

    window.show()

    # Run application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
