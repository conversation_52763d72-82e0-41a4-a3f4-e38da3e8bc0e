#!/usr/bin/env python3
"""
Test script to demonstrate the coordinate precision issue in crosshair placement.

This script shows the difference between using event.position().toPoint() vs event.position()
for mouse coordinate conversion, which causes the pixel offset issue.
"""

import sys
from PyQt6.QtWidgets import QApplication, QGraphicsView, QGraphicsScene, QGraphicsPixmapItem, QVBoxLayout, QWidget, QLabel
from PyQt6.QtCore import Qt, QPointF
from PyQt6.QtGui import QPixmap, QMouseEvent, QPainter, QColor, QPen
from PyQt6.QtCore import QRectF

class CoordinateTestView(QGraphicsView):
    """Test view to demonstrate coordinate precision issues."""
    
    def __init__(self):
        super().__init__()
        self.setMouseTracking(True)
        
        # Create a test scene with a grid pattern
        self.scene = QGraphicsScene()
        self.setScene(self.scene)
        
        # Create a test image with a grid pattern
        self.create_test_image()
        
        # Labels to show coordinate information
        self.info_widget = QWidget()
        self.info_layout = QVBoxLayout(self.info_widget)
        self.mouse_pos_label = QLabel("Mouse Position: ")
        self.scene_pos_label = QLabel("Scene Position: ")
        self.scene_pos_int_label = QLabel("Scene Position (toPoint): ")
        self.snapped_pos_label = QLabel("Snapped Position: ")
        
        self.info_layout.addWidget(self.mouse_pos_label)
        self.info_layout.addWidget(self.scene_pos_label)
        self.info_layout.addWidget(self.scene_pos_int_label)
        self.info_layout.addWidget(self.snapped_pos_label)
        
    def create_test_image(self):
        """Create a test image with a grid pattern to visualize pixel precision."""
        # Create a 200x200 test image with a grid
        pixmap = QPixmap(200, 200)
        pixmap.fill(QColor(255, 255, 255))  # White background
        
        painter = QPainter(pixmap)
        pen = QPen(QColor(200, 200, 200))  # Light gray grid
        pen.setWidth(1)
        painter.setPen(pen)
        
        # Draw grid lines every 10 pixels
        for i in range(0, 201, 10):
            painter.drawLine(i, 0, i, 200)  # Vertical lines
            painter.drawLine(0, i, 200, i)  # Horizontal lines
        
        # Draw pixel boundaries every 5 pixels with darker lines
        pen.setColor(QColor(150, 150, 150))
        painter.setPen(pen)
        for i in range(0, 201, 5):
            painter.drawLine(i, 0, i, 200)
            painter.drawLine(0, i, 200, i)
        
        # Draw individual pixel grid
        pen.setColor(QColor(220, 220, 220))
        pen.setWidth(1)
        painter.setPen(pen)
        for i in range(0, 201, 1):
            painter.drawLine(i, 0, i, 200)
            painter.drawLine(0, i, 200, i)
        
        painter.end()
        
        # Add to scene
        pixmap_item = QGraphicsPixmapItem(pixmap)
        self.scene.addItem(pixmap_item)
        self.scene.setSceneRect(0, 0, 200, 200)
        
    def mouseMoveEvent(self, event: QMouseEvent):
        """Track mouse movement and show coordinate precision."""
        # Get mouse position (floating point)
        mouse_pos = event.position()
        
        # Convert to scene coordinates (floating point)
        scene_pos = self.mapToScene(mouse_pos.toPoint())
        
        # Convert to scene coordinates using integer conversion (current buggy method)
        scene_pos_int = self.mapToScene(mouse_pos.toPoint())
        
        # Convert to scene coordinates using proper floating point method
        scene_pos_float = self.mapToScene(int(mouse_pos.x()), int(mouse_pos.y()))
        
        # Snap to pixel center
        snapped_pos = QPointF(
            round(scene_pos.x()) + 0.5,
            round(scene_pos.y()) + 0.5
        )
        
        # Update labels
        self.mouse_pos_label.setText(f"Mouse Position: ({mouse_pos.x():.3f}, {mouse_pos.y():.3f})")
        self.scene_pos_label.setText(f"Scene Position (float): ({scene_pos.x():.3f}, {scene_pos.y():.3f})")
        self.scene_pos_int_label.setText(f"Scene Position (toPoint): ({scene_pos_int.x():.3f}, {scene_pos_int.y():.3f})")
        self.snapped_pos_label.setText(f"Snapped Position: ({snapped_pos.x():.3f}, {snapped_pos.y():.3f})")
        
        super().mouseMoveEvent(event)
        
    def mousePressEvent(self, event: QMouseEvent):
        """Demonstrate the coordinate precision issue on click."""
        if event.button() == Qt.MouseButton.RightButton:
            print("\n=== COORDINATE PRECISION TEST ===")
            
            # Current buggy method
            scene_pos_buggy = self.mapToScene(event.position().toPoint())
            print(f"BUGGY METHOD - event.position().toPoint(): ({scene_pos_buggy.x():.6f}, {scene_pos_buggy.y():.6f})")
            
            # Correct method - use floating point coordinates
            mouse_pos = event.position()
            scene_pos_correct = self.mapToScene(int(mouse_pos.x()), int(mouse_pos.y()))
            print(f"CORRECT METHOD - mapToScene(int(x), int(y)): ({scene_pos_correct.x():.6f}, {scene_pos_correct.y():.6f})")
            
            # Even better method - preserve sub-pixel precision using QPointF
            mouse_pointf = QPointF(mouse_pos.x(), mouse_pos.y())
            scene_pos_precise = self.mapToScene(mouse_pointf.toPoint())
            print(f"PRECISE METHOD - mapToScene(QPointF): ({scene_pos_precise.x():.6f}, {scene_pos_precise.y():.6f})")
            
            # Show the difference
            diff_x = scene_pos_correct.x() - scene_pos_buggy.x()
            diff_y = scene_pos_correct.y() - scene_pos_buggy.y()
            print(f"DIFFERENCE: ({diff_x:.6f}, {diff_y:.6f}) pixels")
            
            # Snapped positions
            snapped_buggy = QPointF(round(scene_pos_buggy.x()) + 0.5, round(scene_pos_buggy.y()) + 0.5)
            snapped_correct = QPointF(round(scene_pos_correct.x()) + 0.5, round(scene_pos_correct.y()) + 0.5)
            snapped_precise = QPointF(round(scene_pos_precise.x()) + 0.5, round(scene_pos_precise.y()) + 0.5)
            
            print(f"SNAPPED BUGGY: ({snapped_buggy.x():.1f}, {snapped_buggy.y():.1f})")
            print(f"SNAPPED CORRECT: ({snapped_correct.x():.1f}, {snapped_correct.y():.1f})")
            print(f"SNAPPED PRECISE: ({snapped_precise.x():.1f}, {snapped_precise.y():.1f})")
            
        super().mousePressEvent(event)

class CoordinateTestWindow(QWidget):
    """Main window for coordinate precision testing."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Coordinate Precision Test - Right-click to test")
        self.setGeometry(100, 100, 800, 600)
        
        layout = QVBoxLayout()
        
        # Instructions
        instructions = QLabel("""
Coordinate Precision Test

This demonstrates the pixel offset issue in crosshair placement.

Instructions:
1. Move your mouse around to see real-time coordinate tracking
2. Right-click anywhere to see the coordinate precision comparison
3. Look at the console output to see the differences between methods

The current buggy method uses event.position().toPoint() which truncates
floating-point coordinates to integers, causing pixel offset issues.
        """)
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
        
        # Test view
        self.test_view = CoordinateTestView()
        layout.addWidget(self.test_view)
        
        # Coordinate info
        layout.addWidget(self.test_view.info_widget)
        
        self.setLayout(layout)

def main():
    """Run the coordinate precision test."""
    app = QApplication(sys.argv)
    
    window = CoordinateTestWindow()
    window.show()
    
    print("Coordinate Precision Test Started")
    print("Right-click on the grid to see coordinate precision differences")
    print("The issue: event.position().toPoint() truncates floating-point coordinates")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
